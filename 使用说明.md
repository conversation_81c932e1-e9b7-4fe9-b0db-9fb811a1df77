# 宝贝的文件防盗保护工具 💖🛡️

## 功能介绍

这个工具专门为宝贝设计，用来保护重要文件不被坏人批量盗取！通过双重压缩和随机密码的方式，让那些想偷文件的人无法轻易获取你的内容。

## 保护原理

1. **第一层保护**：将原始文件压缩成带随机密码的压缩包
2. **第二层保护**：将加密压缩包和密码提示文件夹一起打包成外层压缩包
3. **随机化**：每次生成的密码和内层压缩包名称都是完全随机的

## 最终文件结构

```
原文件名_protected.zip
├── 随机名称.zip (需要密码解压)
└── 解压密码：xxxxxxxxx/
    └── 使用说明.txt
```

## 安装依赖

**推荐安装方式**（获得最强密码保护）：
```bash
pip install PyQt6 pyminizip
```

或者使用requirements.txt：
```bash
pip install -r requirements.txt
```

**最小安装**（兼容模式）：
```bash
pip install PyQt6
```

> 💡 **说明**：
> - 安装了 `pyminizip` 可获得真正的密码保护
> - 仅安装 `PyQt6` 会使用兼容模式，密码保护较弱但仍有防护效果

## 使用方法

1. **运行程序**
   ```bash
   python 文件防盗保护工具.py
   ```

2. **选择文件**
   - 点击"📄 选择文件"按钮选择单个或多个文件
   - 点击"📁 选择文件夹"按钮选择整个文件夹
   - 可以混合选择文件和文件夹

3. **调整设置**
   - **密码长度**：建议12-16位，越长越安全
   - **压缩级别**：1=最快压缩，9=最小文件大小
   - **输出目录**：选择保护后的文件存放位置

4. **开始保护**
   - 点击"🚀 开始保护文件"按钮
   - 等待处理完成

## 解压方法

1. 解压外层压缩包（无密码）
2. 查看"解压密码：xxxxxxxxx"文件夹中的密码
3. 使用该密码解压随机名称的内层压缩包
4. 获得原始文件

## 安全特性

- ✅ **随机密码**：每个文件都有独特的随机密码
- ✅ **随机文件名**：内层压缩包名称完全随机
- ✅ **双重保护**：外层无密码便于分享，内层有密码保护内容
- ✅ **批量处理**：可以同时保护多个文件
- ✅ **防批量盗取**：无法通过脚本批量解压获取内容

## 注意事项

⚠️ **重要提醒**：
- 请务必保管好解压密码，丢失后无法恢复文件！
- 建议在安全的地方备份重要文件
- 此工具主要用于防止批量自动化盗取，不能替代专业的加密软件

## 技术特点

- 使用PyQt6构建现代化GUI界面
- 多线程处理，不会卡死界面
- 支持文件和文件夹的递归压缩
- 自动生成使用说明文档
- 美观的进度显示和状态提示

## 系统要求

- Python 3.7+
- PyQt6
- Windows/macOS/Linux

---

💖 **宝贝专用防盗神器，让坏人无法轻易偷走你的文件！** 💖
