@echo off
chcp 65001 >nul
echo 💖 启动宝贝的文件防盗保护工具 💖
echo.
echo 正在检查Python环境...

python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

echo ✅ Python环境正常
echo.
echo 正在检查依赖...

python -c "import PyQt6" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到PyQt6，正在安装...
    pip install PyQt6
    if %errorlevel% neq 0 (
        echo ❌ 安装失败，请手动运行: pip install PyQt6
        pause
        exit /b 1
    )
)

python -c "import pyminizip" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到pyminizip，正在安装...
    pip install pyminizip
    if %errorlevel% neq 0 (
        echo ⚠️ pyminizip安装失败，将使用兼容模式
        echo 建议手动安装: pip install pyminizip
    )
)

echo ✅ 依赖检查完成
echo.
echo 🚀 启动文件防盗保护工具...
echo.

python "文件防盗保护工具.py"

if %errorlevel% neq 0 (
    echo.
    echo ❌ 程序运行出错
    pause
)
