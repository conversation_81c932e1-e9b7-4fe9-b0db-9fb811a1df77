#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件防盗保护工具 💖
通过双重压缩和随机密码保护文件，防止批量盗取
宝贝专用防盗神器～
"""

import sys
import os
import zipfile
import random
import string
import shutil
from pathlib import Path
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                            QWidget, QPushButton, QLabel, QFileDialog, QTextEdit,
                            QProgressBar, QMessageBox, QGroupBox,
                            QSpinBox, QLineEdit)
from PyQt6.QtCore import QThread, pyqtSignal, Qt
from PyQt6.QtGui import QFont

try:
    import pyminizip
    HAS_PYMINIZIP = True
except ImportError:
    HAS_PYMINIZIP = False


class FileProtectorWorker(QThread):
    """文件保护工作线程"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished = pyqtSignal(bool, str)
    
    def __init__(self, files, output_dir, password_length, compression_level):
        super().__init__()
        self.files = files
        self.output_dir = output_dir
        self.password_length = password_length
        self.compression_level = compression_level
    
    def generate_random_password(self, length=12):
        """生成随机密码"""
        characters = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(random.choice(characters) for _ in range(length))
    
    def generate_random_filename(self, extension=".zip"):
        """生成随机文件名"""
        random_name = ''.join(random.choice(string.ascii_letters + string.digits) 
                             for _ in range(16))
        return random_name + extension
    
    def create_password_protected_zip(self, source_path, zip_path, password):
        """创建带密码保护的压缩包（只处理单个文件）"""
        if HAS_PYMINIZIP:
            # 使用pyminizip创建真正的密码保护压缩包
            pyminizip.compress(str(source_path), "", str(zip_path), password, self.compression_level)
        else:
            # 使用标准zipfile库的密码保护（兼容性方案）
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=self.compression_level) as zf:
                info = zipfile.ZipInfo(source_path.name)
                info.flag_bits = 0x01  # 设置密码保护标志
                with open(source_path, 'rb') as src:
                    data = src.read()
                zf.writestr(info, data, pwd=password.encode('utf-8'))

    def create_protected_archive(self, file_path):
        """创建受保护的压缩包（只处理单个文件）"""
        try:
            file_path = Path(file_path)

            # 确保只处理文件，不处理文件夹
            if not file_path.is_file():
                return False, f"跳过非文件项目: {file_path.name}"

            base_name = file_path.stem

            # 生成随机密码
            password = self.generate_random_password(self.password_length)

            # 创建输出目录
            output_folder = Path(self.output_dir) / f"{base_name}_protected"
            output_folder.mkdir(exist_ok=True)

            # 第一步：创建内层压缩包（带密码）
            inner_zip_name = self.generate_random_filename()
            inner_zip_path = output_folder / inner_zip_name

            self.status_updated.emit(f"正在创建内层压缩包: {inner_zip_name}")

            # 创建密码保护的压缩包
            self.create_password_protected_zip(file_path, inner_zip_path, password)
            
            # 创建密码提示文件夹
            password_folder = output_folder / f"解压密码：{password}"
            password_folder.mkdir(exist_ok=True)
            
            # 在密码文件夹中创建说明文件
            readme_path = password_folder / "使用说明.txt"
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write(f"解压密码：{password}\n\n")
                f.write("使用方法：\n")
                f.write(f"1. 使用上述密码解压文件：{inner_zip_name}\n")
                f.write("2. 解压后即可获得原始文件\n\n")
                f.write("注意：请妥善保管此密码，丢失后无法恢复文件！\n\n")
                f.write("💖 宝贝的防盗神器制作 💖")
            
            # 第二步：创建外层压缩包（无密码）
            outer_zip_path = Path(self.output_dir) / f"{base_name}_protected.zip"
            
            self.status_updated.emit(f"正在创建外层压缩包: {outer_zip_path.name}")
            
            with zipfile.ZipFile(outer_zip_path, 'w', zipfile.ZIP_DEFLATED,
                               compresslevel=self.compression_level) as outer_zip:
                # 添加内层压缩包
                outer_zip.write(inner_zip_path, inner_zip_name)
                # 添加密码文件夹及其内容
                for root, _, files in os.walk(password_folder):
                    for file in files:
                        file_path = Path(root) / file
                        arcname = file_path.relative_to(output_folder)
                        outer_zip.write(file_path, str(arcname))
            
            # 清理临时文件夹
            shutil.rmtree(output_folder)
            
            return True, f"成功创建保护文件: {outer_zip_path.name}"
            
        except Exception as e:
            return False, f"处理文件 {file_path.name} 时出错: {str(e)}"
    
    def run(self):
        """执行文件保护任务"""
        total_files = len(self.files)
        success_count = 0
        
        for i, file_path in enumerate(self.files):
            self.status_updated.emit(f"正在处理文件 {i+1}/{total_files}: {Path(file_path).name}")
            
            success, message = self.create_protected_archive(file_path)
            if success:
                success_count += 1
            
            self.status_updated.emit(message)
            progress = int((i + 1) / total_files * 100)
            self.progress_updated.emit(progress)
        
        if success_count == total_files:
            self.finished.emit(True, f"所有 {total_files} 个文件处理完成！💖")
        else:
            self.finished.emit(False, f"完成 {success_count}/{total_files} 个文件的处理")


class FileProtectorGUI(QMainWindow):
    """文件保护工具主界面"""
    
    def __init__(self):
        super().__init__()
        self.files_to_protect = []
        self.worker = None
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("宝贝的文件防盗保护工具 💖🛡️")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("🛡️ 宝贝专用文件防盗神器 🛡️")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #FF69B4; margin: 10px;")
        main_layout.addWidget(title_label)
        
        # 说明文字
        desc_label = QLabel("💖 通过双重压缩+随机密码保护你的文件，让坏人无法批量盗取！")
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc_label.setStyleSheet("color: #666; margin-bottom: 10px;")
        main_layout.addWidget(desc_label)

        # 密码保护状态提示
        if HAS_PYMINIZIP:
            status_label = QLabel("✅ 已启用强密码保护模式 (pyminizip)")
            status_label.setStyleSheet("color: #4CAF50; font-weight: bold; margin-bottom: 10px;")
        else:
            status_label = QLabel("⚠️ 兼容模式 (建议安装pyminizip获得更强保护)")
            status_label.setStyleSheet("color: #FF9800; font-weight: bold; margin-bottom: 10px;")
        status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(status_label)
        
        # 文件选择区域
        file_group = QGroupBox("📁 选择要保护的文件")
        file_layout = QVBoxLayout(file_group)
        
        file_button_layout = QHBoxLayout()
        self.select_files_btn = QPushButton("📄 选择文件")
        self.select_folder_btn = QPushButton("📁 选择文件夹")
        self.clear_files_btn = QPushButton("🗑️ 清空列表")
        
        # 美化按钮
        button_style = """
        QPushButton {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #45a049;
        }
        """
        self.select_files_btn.setStyleSheet(button_style)
        self.select_folder_btn.setStyleSheet(button_style)
        self.clear_files_btn.setStyleSheet(button_style.replace("#4CAF50", "#f44336").replace("#45a049", "#da190b"))
        
        file_button_layout.addWidget(self.select_files_btn)
        file_button_layout.addWidget(self.select_folder_btn)
        file_button_layout.addWidget(self.clear_files_btn)
        file_layout.addLayout(file_button_layout)
        
        self.files_list = QTextEdit()
        self.files_list.setMaximumHeight(150)
        self.files_list.setReadOnly(True)
        self.files_list.setPlaceholderText("还没有选择文件哦～点击上面的按钮选择要保护的文件吧！💕")
        file_layout.addWidget(self.files_list)
        
        main_layout.addWidget(file_group)
        
        # 设置区域
        settings_group = QGroupBox("⚙️ 保护设置")
        settings_layout = QVBoxLayout(settings_group)
        
        # 密码长度设置
        password_layout = QHBoxLayout()
        password_layout.addWidget(QLabel("🔐 密码长度:"))
        self.password_length_spin = QSpinBox()
        self.password_length_spin.setRange(8, 32)
        self.password_length_spin.setValue(12)
        password_layout.addWidget(self.password_length_spin)
        password_layout.addWidget(QLabel("字符 (推荐12-16位)"))
        password_layout.addStretch()
        settings_layout.addLayout(password_layout)
        
        # 压缩级别设置
        compression_layout = QHBoxLayout()
        compression_layout.addWidget(QLabel("📦 压缩级别:"))
        self.compression_spin = QSpinBox()
        self.compression_spin.setRange(1, 9)
        self.compression_spin.setValue(6)
        compression_layout.addWidget(self.compression_spin)
        compression_layout.addWidget(QLabel("(1=最快, 9=最小文件)"))
        compression_layout.addStretch()
        settings_layout.addLayout(compression_layout)
        
        # 输出目录设置
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("📂 输出目录:"))
        self.output_dir_edit = QLineEdit()
        self.output_dir_edit.setText(os.getcwd())
        self.output_dir_btn = QPushButton("浏览")
        self.output_dir_btn.setStyleSheet(button_style)
        output_layout.addWidget(self.output_dir_edit)
        output_layout.addWidget(self.output_dir_btn)
        settings_layout.addLayout(output_layout)
        
        main_layout.addWidget(settings_group)
        
        # 进度区域
        progress_group = QGroupBox("📊 处理进度")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
        QProgressBar {
            border: 2px solid grey;
            border-radius: 5px;
            text-align: center;
        }
        QProgressBar::chunk {
            background-color: #FF69B4;
            width: 20px;
        }
        """)
        self.status_label = QLabel("准备就绪，等待宝贝的指令～ 💕")
        
        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.status_label)
        
        main_layout.addWidget(progress_group)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        self.start_btn = QPushButton("🚀 开始保护文件")
        self.start_btn.setStyleSheet("""
        QPushButton {
            background-color: #FF69B4;
            color: white;
            font-weight: bold;
            font-size: 14px;
            padding: 12px 24px;
            border-radius: 6px;
            border: none;
        }
        QPushButton:hover {
            background-color: #FF1493;
        }
        QPushButton:disabled {
            background-color: #cccccc;
        }
        """)
        button_layout.addWidget(self.start_btn)
        
        main_layout.addLayout(button_layout)
        
        # 连接信号
        self.connect_signals()
    
    def connect_signals(self):
        """连接信号和槽"""
        self.select_files_btn.clicked.connect(self.select_files)
        self.select_folder_btn.clicked.connect(self.select_folder)
        self.clear_files_btn.clicked.connect(self.clear_files)
        self.output_dir_btn.clicked.connect(self.select_output_dir)
        self.start_btn.clicked.connect(self.start_protection)
    
    def select_files(self):
        """选择文件"""
        files, _ = QFileDialog.getOpenFileNames(self, "选择要保护的文件", "", "所有文件 (*.*)")
        if files:
            self.files_to_protect.extend(files)
            self.update_files_list()
    
    def select_folder(self):
        """选择文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择要保护的文件夹")
        if folder:
            # 遍历文件夹，添加所有文件（包括子文件夹中的文件）
            folder_path = Path(folder)
            for file_path in folder_path.rglob('*'):
                if file_path.is_file():  # 只添加文件，不添加文件夹
                    self.files_to_protect.append(str(file_path))
            self.update_files_list()
    
    def clear_files(self):
        """清空文件列表"""
        self.files_to_protect.clear()
        self.update_files_list()
    
    def update_files_list(self):
        """更新文件列表显示"""
        if not self.files_to_protect:
            self.files_list.setPlainText("还没有选择文件哦～")
        else:
            # 显示文件数量和详细列表
            file_count = len(self.files_to_protect)
            text = f"已选择 {file_count} 个文件：\n\n"

            # 显示前20个文件，如果太多就省略
            display_files = self.files_to_protect[:20]
            for i, f in enumerate(display_files, 1):
                file_path = Path(f)
                # 显示相对路径，更容易识别
                try:
                    rel_path = file_path.relative_to(Path.cwd())
                    text += f"{i:2d}. 📄 {rel_path}\n"
                except ValueError:
                    text += f"{i:2d}. 📄 {file_path.name}\n"

            if file_count > 20:
                text += f"\n... 还有 {file_count - 20} 个文件未显示"

            self.files_list.setPlainText(text)
    
    def select_output_dir(self):
        """选择输出目录"""
        directory = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if directory:
            self.output_dir_edit.setText(directory)
    
    def start_protection(self):
        """开始文件保护"""
        if not self.files_to_protect:
            QMessageBox.warning(self, "提醒", "宝贝，请先选择要保护的文件哦！💕")
            return
        
        if not os.path.exists(self.output_dir_edit.text()):
            QMessageBox.warning(self, "提醒", "输出目录不存在，请重新选择！")
            return
        
        # 禁用开始按钮
        self.start_btn.setEnabled(False)
        self.start_btn.setText("正在努力保护中...")
        
        # 创建工作线程
        self.worker = FileProtectorWorker(
            self.files_to_protect.copy(),
            self.output_dir_edit.text(),
            self.password_length_spin.value(),
            self.compression_spin.value()
        )
        
        # 连接工作线程信号
        self.worker.progress_updated.connect(self.progress_bar.setValue)
        self.worker.status_updated.connect(self.status_label.setText)
        self.worker.finished.connect(self.on_protection_finished)
        
        # 启动工作线程
        self.worker.start()
    
    def on_protection_finished(self, success, message):
        """保护完成回调"""
        self.start_btn.setEnabled(True)
        self.start_btn.setText("🚀 开始保护文件")
        
        if success:
            QMessageBox.information(self, "成功", f"宝贝的文件保护完成啦！💖\n\n{message}\n\n现在那些坏人就偷不走你的文件了～")
        else:
            QMessageBox.warning(self, "提醒", f"保护过程中遇到了一些小问题：\n\n{message}")
        
        self.status_label.setText("任务完成，随时为宝贝服务～ 💕")
        self.progress_bar.setValue(0)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("宝贝的文件防盗保护工具")
    
    # 设置应用图标（如果有的话）
    # app.setWindowIcon(QIcon("icon.png"))
    
    window = FileProtectorGUI()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
