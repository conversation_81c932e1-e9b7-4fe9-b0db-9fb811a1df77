#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件防盗保护工具的核心功能
"""

import os
import sys
from pathlib import Path

# 导入我们的工具
from 文件防盗保护工具 import FileProtectorWorker

def test_file_protection():
    """测试文件保护功能"""
    print("🧪 开始测试文件防盗保护工具...")
    
    # 创建测试文件列表
    test_files = [
        "测试文件.txt",
        "测试文件夹/文件1.txt", 
        "测试文件夹/文件2.txt",
        "测试文件夹/子文件夹/子文件.txt"
    ]
    
    # 检查测试文件是否存在
    existing_files = []
    for file_path in test_files:
        if Path(file_path).exists():
            existing_files.append(str(Path(file_path).absolute()))
            print(f"✅ 找到测试文件: {file_path}")
        else:
            print(f"❌ 测试文件不存在: {file_path}")
    
    if not existing_files:
        print("❌ 没有找到测试文件，请先创建测试文件")
        return False
    
    print(f"\n📁 将处理 {len(existing_files)} 个文件")
    
    # 创建输出目录
    output_dir = Path("测试输出")
    output_dir.mkdir(exist_ok=True)
    
    # 创建工作线程（但不启动Qt事件循环）
    worker = FileProtectorWorker(
        files=existing_files,
        output_dir=str(output_dir),
        password_length=12,
        compression_level=6
    )
    
    # 手动执行处理
    print("\n🚀 开始处理文件...")
    
    success_count = 0
    for i, file_path in enumerate(existing_files):
        print(f"\n处理文件 {i+1}/{len(existing_files)}: {Path(file_path).name}")
        
        success, message = worker.create_protected_archive(file_path)
        print(f"结果: {message}")
        
        if success:
            success_count += 1
    
    print(f"\n📊 处理完成！")
    print(f"✅ 成功: {success_count}/{len(existing_files)} 个文件")
    
    # 检查输出文件
    print(f"\n📂 输出目录内容:")
    for item in output_dir.iterdir():
        if item.is_file():
            size = item.stat().st_size
            print(f"  📦 {item.name} ({size} 字节)")
    
    return success_count == len(existing_files)

if __name__ == "__main__":
    try:
        success = test_file_protection()
        if success:
            print("\n🎉 测试成功！所有文件都已被保护！")
        else:
            print("\n⚠️ 测试部分成功，请检查错误信息")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
